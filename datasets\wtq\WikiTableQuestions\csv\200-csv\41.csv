"Number","Encoding","Implied probability"
"1 = 20 + 0","1","1/2"
"2 = 21 + 0","010","1/8"
"3 = 21 + 1","011","1/8"
"4 = 22 + 0","00100","1/32"
"5 = 22 + 1","00101","1/32"
"6 = 22 + 2","00110","1/32"
"7 = 22 + 3","00111","1/32"
"8 = 23 + 0","0001000","1/128"
"9 = 23 + 1","0001001","1/128"
"10 = 23 + 2","0001010","1/128"
"11 = 23 + 3","0001011","1/128"
"12 = 23 + 4","0001100","1/128"
"13 = 23 + 5","0001101","1/128"
"14 = 23 + 6","0001110","1/128"
"15 = 23 + 7","0001111","1/128"
"16 = 24 + 0","000010000","1/512"
"17 = 24 + 1","000010001","1/512"
