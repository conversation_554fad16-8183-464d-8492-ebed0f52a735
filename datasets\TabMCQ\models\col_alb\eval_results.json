{"acc": 0.9637237762237763, "roc": 0.9888186106515263, "f1": 0.9090909090909091, "positive_fraction": 0.19875437062937062, "eval_loss": 0.1315067857503891, "hypers": {"local_rank": 0, "global_rank": 0, "world_size": 2, "model_type": "<PERSON>bert", "model_name_or_path": "albert-base-v2", "resume_from": "/root/experiment/row-column-intersection/datasets/TabMCQ/models/col_alb", "config_name": "", "tokenizer_name": "", "cache_dir": "", "do_lower_case": true, "gradient_accumulation_steps": 2, "learning_rate": 2e-05, "weight_decay": 0.01, "adam_epsilon": 1e-08, "max_grad_norm": 1.0, "warmup_instances": 10000, "num_train_epochs": 5, "no_cuda": false, "n_gpu": 1, "seed": 42, "fp16": false, "fp16_opt_level": "O1", "full_train_batch_size": 128, "per_gpu_eval_batch_size": 8, "output_dir": "/root/experiment/row-column-intersection/datasets/TabMCQ/models/col_alb", "save_total_limit": 1, "save_steps": 0, "use_tensorboard": false, "log_on_all_nodes": false, "server_ip": "", "server_port": "", "__required_args__": ["model_type", "model_name_or_path"], "max_seq_length": 256, "num_labels": 2, "single_sequence": false, "additional_special_tokens": "", "is_separate": false, "kd_alpha": 0.9, "kd_temperature": 10.0, "train_dir": "/root/experiment/row-column-intersection/datasets/TabMCQ/train/col.jsonl.gz", "dev_dir": "/root/experiment/row-column-intersection/datasets/TabMCQ/dev/col.jsonl.gz", "train_instances": 26743, "hyper_tune": 0, "prune_after": 5, "save_per_epoch": true, "teacher_labels": "", "per_gpu_train_batch_size": 32, "stop_time": null}}