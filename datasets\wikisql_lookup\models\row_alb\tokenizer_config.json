{"added_tokens_decoder": {"0": {"content": "<pad>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "1": {"content": "<unk>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "2": {"content": "[CLS]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "3": {"content": "[SEP]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "4": {"content": "[MASK]", "lstrip": true, "normalized": false, "rstrip": false, "single_word": false, "special": true}}, "bos_token": "[CLS]", "clean_up_tokenization_spaces": false, "cls_token": "[CLS]", "do_lower_case": true, "eos_token": "[SEP]", "keep_accents": false, "mask_token": "[MASK]", "model_max_length": 512, "pad_token": "<pad>", "remove_space": true, "sep_token": "[SEP]", "sp_model_kwargs": {}, "tokenizer_class": "<PERSON><PERSON><PERSON><PERSON>", "unk_token": "<unk>"}