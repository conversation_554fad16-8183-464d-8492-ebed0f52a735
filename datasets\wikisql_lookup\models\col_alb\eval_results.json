{"acc": 0.9885219332040279, "roc": 0.9953540842586317, "f1": 0.9638130340090466, "positive_fraction": 0.15986927757260144, "eval_loss": 0.053343487144728696, "hypers": {"local_rank": -1, "global_rank": 0, "world_size": 1, "model_type": "<PERSON>bert", "model_name_or_path": "albert-base-v2", "resume_from": "/root/experiment/row-column-intersection/datasets/wikisql_lookup/models/col_alb", "config_name": "", "tokenizer_name": "", "cache_dir": "", "do_lower_case": true, "gradient_accumulation_steps": 2, "learning_rate": 2e-05, "weight_decay": 0.01, "adam_epsilon": 1e-08, "max_grad_norm": 1.0, "warmup_instances": 100000, "num_train_epochs": 3, "no_cuda": false, "n_gpu": 1, "seed": 42, "fp16": false, "fp16_opt_level": "O1", "full_train_batch_size": 64, "per_gpu_eval_batch_size": 8, "output_dir": "/root/experiment/row-column-intersection/datasets/wikisql_lookup/models/col_alb", "save_total_limit": 1, "save_steps": 0, "use_tensorboard": false, "log_on_all_nodes": false, "server_ip": "", "server_port": "", "__required_args__": ["model_type", "model_name_or_path"], "max_seq_length": 256, "num_labels": 2, "single_sequence": false, "additional_special_tokens": "", "is_separate": false, "kd_alpha": 0.9, "kd_temperature": 10.0, "train_dir": "/root/experiment/row-column-intersection/datasets/wikisql_lookup/train/col.jsonl.gz", "dev_dir": "/root/experiment/row-column-intersection/datasets/wikisql_lookup/dev/col.jsonl.gz", "train_instances": 360664, "hyper_tune": 0, "prune_after": 5, "save_per_epoch": false, "teacher_labels": "", "per_gpu_train_batch_size": 32, "stop_time": null}}